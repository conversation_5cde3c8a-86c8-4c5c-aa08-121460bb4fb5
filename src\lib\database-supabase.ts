// Supabase database service layer
// This replaces the SQLite database.ts with Supabase functionality

import { supabaseAdmin } from './supabase';

// Database initialization (Supabase handles this automatically)
export const initDatabase = async (): Promise<void> => {
  // With Supabase, the database is already initialized
  // We can optionally run any setup checks here
  try {
    // Test connection
    const { error } = await supabaseAdmin.from('migrations').select('count').limit(1);
    if (error) {
      console.error('Database connection test failed:', error);
      throw new Error('Failed to connect to Supabase database');
    }
    console.log('Supabase database connection established');
  } catch (error) {
    console.error('Database initialization error:', error);
    throw error;
  }
};

// Get database instance (returns Supabase client)
export const getDatabase = async () => {
  // Return the Supabase admin client for server-side operations
  return supabaseAdmin;
};

// Create database tables (handled by Supabase migrations)
const createTables = async (): Promise<void> => {
  // Tables are created via Supabase SQL migrations
  // This function is kept for compatibility but doesn't need to do anything
  console.log('Tables are managed by Supabase migrations');
};

// Close database connection (not needed with Supabase)
export const closeDatabase = (): void => {
  // Supabase handles connection pooling automatically
  console.log('Supabase connections are managed automatically');
};

// Utility function to run migrations
export const runMigrations = async (): Promise<void> => {
  const db = await getDatabase();
  
  // Check if migrations table exists and is accessible
  const { data: existingMigrations, error } = await db
    .from('migrations')
    .select('name');
    
  if (error) {
    console.error('Error checking migrations:', error);
    throw error;
  }

  // Define migrations that should be run
  const migrations: Array<{name: string; description: string}> = [
    {
      name: '001_initial_schema',
      description: 'Initial database schema with users, templates, documents tables'
    }
    // Add future migrations here
  ];

  const executedMigrations = existingMigrations?.map(m => m.name) || [];

  for (const migration of migrations) {
    if (!executedMigrations.includes(migration.name)) {
      // For Supabase, migrations are typically run via the dashboard or CLI
      // This is more for tracking purposes
      const { error: insertError } = await db
        .from('migrations')
        .insert({ name: migration.name });
        
      if (insertError) {
        console.error(`Failed to record migration ${migration.name}:`, insertError);
      } else {
        console.log(`Migration ${migration.name} recorded successfully`);
      }
    }
  }
};

// Helper function to handle Supabase errors
export const handleSupabaseError = (error: any, operation: string) => {
  console.error(`Supabase ${operation} error:`, error);
  
  if (error.code === 'PGRST116') {
    throw new Error(`No rows found for ${operation}`);
  }
  
  if (error.code === '23505') {
    throw new Error(`Duplicate entry for ${operation}`);
  }
  
  if (error.code === '23503') {
    throw new Error(`Foreign key constraint violation for ${operation}`);
  }
  
  throw new Error(`Database ${operation} failed: ${error.message}`);
};

// Utility function to convert SQLite-style queries to Supabase
export const convertDateFilter = (sqliteDate: string): string => {
  // Convert SQLite date functions to PostgreSQL equivalents
  return sqliteDate
    .replace(/DATE\('now'\)/g, 'CURRENT_DATE')
    .replace(/DATE\('now', '([^']+)'\)/g, "CURRENT_DATE + INTERVAL '$1'")
    .replace(/-(\d+) days/g, '- $1 days');
};

// Export for backward compatibility
export { createTables };
