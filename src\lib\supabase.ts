import { createClient } from '@supabase/supabase-js'
import type { SupabaseClient } from '@supabase/supabase-js'

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY

let supabase: SupabaseClient | null = null
let supabaseAdmin: SupabaseClient | null = null

if (supabaseUrl && supabaseAnonKey) {
  // Client for browser/client-side operations
  supabase = createClient(supabaseUrl, supabaseAnonKey)
}

if (supabaseUrl && supabaseServiceRoleKey) {
  // Admin client for server-side operations (bypasses RLS)
  supabaseAdmin = createClient(supabaseUrl, supabaseServiceRoleKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  })
}

export { supabase, supabaseAdmin }

// Database types (will be generated from Supabase)
export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: number
          username: string
          password_hash: string
          recovery_options: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: number
          username: string
          password_hash: string
          recovery_options?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: number
          username?: string
          password_hash?: string
          recovery_options?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      templates: {
        Row: {
          id: string
          name: string
          description: string | null
          filename: string
          placeholders: string | null
          layout_size: string
          has_applicant_photo: boolean
          uploaded_at: string
          updated_at: string
        }
        Insert: {
          id: string
          name: string
          description?: string | null
          filename: string
          placeholders?: string | null
          layout_size?: string
          has_applicant_photo?: boolean
          uploaded_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          filename?: string
          placeholders?: string | null
          layout_size?: string
          has_applicant_photo?: boolean
          uploaded_at?: string
          updated_at?: string
        }
      }
      documents: {
        Row: {
          id: number
          template_id: string
          document_data: string | null
          generated_at: string
        }
        Insert: {
          id?: number
          template_id: string
          document_data?: string | null
          generated_at?: string
        }
        Update: {
          id?: number
          template_id?: string
          document_data?: string | null
          generated_at?: string
        }
      }
      migrations: {
        Row: {
          id: number
          name: string
          executed_at: string
        }
        Insert: {
          id?: number
          name: string
          executed_at?: string
        }
        Update: {
          id?: number
          name?: string
          executed_at?: string
        }
      }
    }
  }
}
