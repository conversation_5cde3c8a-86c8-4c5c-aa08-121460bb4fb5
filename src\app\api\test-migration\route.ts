import { NextRequest, NextResponse } from 'next/server';
import { testMigration, checkEnvironmentSetup } from '@/lib/test-migration';

export async function GET(request: NextRequest) {
  try {
    // Check if environment is properly set up
    const envCheck = checkEnvironmentSetup();
    if (!envCheck) {
      return NextResponse.json({
        success: false,
        message: 'Environment setup incomplete',
        error: 'Missing required environment variables'
      }, { status: 400 });
    }

    // Run migration tests
    const result = await testMigration();
    
    if (result.success) {
      return NextResponse.json({
        success: true,
        message: 'Migration test completed successfully',
        performanceMs: result.performanceMs,
        timestamp: new Date().toISOString()
      });
    } else {
      return NextResponse.json({
        success: false,
        message: result.message,
        error: result.error
      }, { status: 500 });
    }
  } catch (error) {
    console.error('Migration test API error:', error);
    return NextResponse.json({
      success: false,
      message: 'Migration test failed',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  // Same as GET for now, but could be extended for different test scenarios
  return GET(request);
}
