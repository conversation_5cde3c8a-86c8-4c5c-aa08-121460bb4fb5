'use client';

import { useOfflineSync } from '@/hooks/useOfflineSync';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Wifi, WifiOff, Database, Cloud, RefreshCw, Settings } from 'lucide-react';

interface DatabaseStatusIndicatorProps {
  showDetails?: boolean;
  showSyncButton?: boolean;
  className?: string;
}

export default function DatabaseStatusIndicator({ 
  showDetails = false, 
  showSyncButton = false,
  className = ""
}: DatabaseStatusIndicatorProps) {
  const {
    isOnline,
    isSupabaseMode,
    isSQLiteMode,
    syncStatus,
    sync,
    toggleAutoSync,
    isSyncing,
    lastSyncResult,
    error
  } = useOfflineSync();

  const handleSync = async () => {
    await sync();
  };

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      {/* Connection Status */}
      <div className="flex items-center space-x-1">
        {isOnline ? (
          <Wifi className="w-4 h-4 text-green-500" />
        ) : (
          <WifiOff className="w-4 h-4 text-red-500" />
        )}
        
        {/* Database Mode Badge */}
        <Badge 
          variant={isSupabaseMode ? "default" : "secondary"}
          className="text-xs"
        >
          {isSupabaseMode ? (
            <>
              <Cloud className="w-3 h-3 mr-1" />
              Online
            </>
          ) : (
            <>
              <Database className="w-3 h-3 mr-1" />
              Offline
            </>
          )}
        </Badge>
      </div>

      {/* Sync Button */}
      {showSyncButton && (
        <Button
          size="sm"
          variant="outline"
          onClick={handleSync}
          disabled={isSyncing || !isOnline}
          className="text-xs"
        >
          <RefreshCw className={`w-3 h-3 mr-1 ${isSyncing ? 'animate-spin' : ''}`} />
          {isSyncing ? 'Syncing...' : 'Sync'}
        </Button>
      )}

      {/* Detailed Status */}
      {showDetails && (
        <div className="text-xs text-gray-600 space-y-1">
          <div className="flex items-center space-x-2">
            <span>Mode: {isSupabaseMode ? 'Supabase (Cloud)' : 'SQLite (Local)'}</span>
            
            {syncStatus && (
              <Button
                size="sm"
                variant="ghost"
                onClick={toggleAutoSync}
                className="text-xs p-1 h-auto"
                title={`Auto-sync is ${syncStatus.autoSyncEnabled ? 'enabled' : 'disabled'}`}
              >
                <Settings className={`w-3 h-3 ${syncStatus.autoSyncEnabled ? 'text-green-500' : 'text-gray-400'}`} />
              </Button>
            )}
          </div>
          
          {syncStatus && (
            <div className="space-y-1">
              {syncStatus.lastSync && (
                <div>Last sync: {new Date(syncStatus.lastSync).toLocaleTimeString()}</div>
              )}
              
              {syncStatus.pendingChanges > 0 && (
                <div className="text-orange-600">
                  {syncStatus.pendingChanges} pending changes
                </div>
              )}
            </div>
          )}
          
          {lastSyncResult && !lastSyncResult.success && (
            <div className="text-red-600">
              Sync failed: {lastSyncResult.errors.join(', ')}
            </div>
          )}
          
          {error && (
            <div className="text-red-600">
              Error: {error}
            </div>
          )}
        </div>
      )}
    </div>
  );
}
