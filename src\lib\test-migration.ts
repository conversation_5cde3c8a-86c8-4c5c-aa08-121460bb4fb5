// Test script to validate Supabase migration
// Run this to ensure all database operations work correctly

import { initDatabase, runMigrations } from './database';
import { UserModel, signup, login, hashPassword } from './models/user';
import { TemplateModel, DocumentModel } from './models/template';

export async function testMigration() {
  console.log('🧪 Starting Supabase migration tests...\n');
  
  try {
    // Test 1: Database initialization
    console.log('1. Testing database initialization...');
    await initDatabase();
    console.log('✅ Database initialized successfully\n');

    // Test 2: Run migrations
    console.log('2. Testing migrations...');
    await runMigrations();
    console.log('✅ Migrations completed successfully\n');

    // Test 3: User operations
    console.log('3. Testing user operations...');
    
    // Create test user
    const testUsername = `test_user_${Date.now()}`;
    const testPassword = 'test_password_123';
    
    const signupResult = await signup({
      username: testUsername,
      password: testPassword,
      recoveryMethod: 'privateKey',
      privateKey: 'test_private_key_123'
    });
    
    if (!signupResult.success) {
      throw new Error(`Signup failed: ${signupResult.message}`);
    }
    console.log('✅ User signup successful');

    // Test login
    const loginResult = await login({
      username: testUsername,
      password: testPassword
    });
    
    if (!loginResult.success) {
      throw new Error(`Login failed: ${loginResult.message}`);
    }
    console.log('✅ User login successful');

    // Test user retrieval
    const user = await UserModel.findByUsername(testUsername);
    if (!user) {
      throw new Error('User not found after creation');
    }
    console.log('✅ User retrieval successful');

    // Clean up test user
    await UserModel.deleteUser(testUsername);
    console.log('✅ User cleanup successful\n');

    // Test 4: Template operations
    console.log('4. Testing template operations...');
    
    const testTemplateId = `test_template_${Date.now()}`;
    const testTemplate = {
      id: testTemplateId,
      name: 'Test Template',
      description: 'A test template for migration validation',
      filename: 'test-template.html',
      placeholders: ['{{name}}', '{{date}}'],
      layoutSize: 'A4' as const,
      hasApplicantPhoto: false
    };

    // Create template
    const createdTemplate = await TemplateModel.create(testTemplate);
    if (!createdTemplate) {
      throw new Error('Template creation failed');
    }
    console.log('✅ Template creation successful');

    // Retrieve template
    const retrievedTemplate = await TemplateModel.findById(testTemplateId);
    if (!retrievedTemplate) {
      throw new Error('Template retrieval failed');
    }
    console.log('✅ Template retrieval successful');

    // Update template
    const updateResult = await TemplateModel.update(testTemplateId, {
      description: 'Updated test template description'
    });
    if (!updateResult) {
      throw new Error('Template update failed');
    }
    console.log('✅ Template update successful');

    // Search templates
    const searchResults = await TemplateModel.search('Test');
    if (searchResults.length === 0) {
      throw new Error('Template search failed');
    }
    console.log('✅ Template search successful');

    // Test 5: Document operations
    console.log('5. Testing document operations...');
    
    const testDocument = {
      templateId: testTemplateId,
      documentData: {
        name: 'John Doe',
        date: new Date().toISOString()
      }
    };

    // Create document
    const createdDocument = await DocumentModel.create(testDocument);
    if (!createdDocument) {
      throw new Error('Document creation failed');
    }
    console.log('✅ Document creation successful');

    // Retrieve documents by template
    const templateDocuments = await DocumentModel.findByTemplateId(testTemplateId);
    if (templateDocuments.length === 0) {
      throw new Error('Document retrieval failed');
    }
    console.log('✅ Document retrieval successful');

    // Get document stats
    const stats = await DocumentModel.getDocumentStats();
    if (typeof stats.totalDocuments !== 'number') {
      throw new Error('Document stats retrieval failed');
    }
    console.log('✅ Document stats retrieval successful');

    // Clean up test data
    await DocumentModel.delete(createdDocument.id!);
    await TemplateModel.delete(testTemplateId);
    console.log('✅ Test data cleanup successful\n');

    // Test 6: Performance check
    console.log('6. Testing performance...');
    const startTime = Date.now();
    
    // Perform multiple operations
    await Promise.all([
      TemplateModel.findAll(),
      DocumentModel.getDocumentStats(),
      UserModel.getAllUsers()
    ]);
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    console.log(`✅ Performance test completed in ${duration}ms\n`);

    console.log('🎉 All migration tests passed successfully!');
    console.log('Your Supabase integration is working correctly.\n');
    
    return {
      success: true,
      message: 'All tests passed',
      performanceMs: duration
    };

  } catch (error) {
    console.error('❌ Migration test failed:', error);
    console.log('\n🔧 Troubleshooting tips:');
    console.log('1. Check your .env.local file has correct Supabase credentials');
    console.log('2. Ensure you ran the SQL schema in your Supabase dashboard');
    console.log('3. Verify your Supabase project is active and accessible');
    console.log('4. Check the Supabase dashboard for any error logs\n');
    
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error',
      error
    };
  }
}

// Helper function to check environment setup
export function checkEnvironmentSetup() {
  console.log('🔍 Checking environment setup...\n');
  
  const requiredEnvVars = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    'SUPABASE_SERVICE_ROLE_KEY'
  ];
  
  const missing = requiredEnvVars.filter(envVar => !process.env[envVar]);
  
  if (missing.length > 0) {
    console.log('❌ Missing required environment variables:');
    missing.forEach(envVar => {
      console.log(`   - ${envVar}`);
    });
    console.log('\n📝 Please add these to your .env.local file');
    return false;
  }
  
  console.log('✅ All required environment variables are set');
  
  // Check if URLs look correct
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  if (supabaseUrl && !supabaseUrl.includes('supabase.co')) {
    console.log('⚠️  Warning: Supabase URL doesn\'t look like a standard Supabase URL');
  }
  
  return true;
}

// Export for use in API routes or scripts
export default testMigration;
