// Database abstraction layer - environment-based switching
// Uses Supabase if configured, otherwise falls back to SQLite

const useSupabase = process.env.NEXT_PUBLIC_SUPABASE_URL &&
                   process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY &&
                   process.env.SUPABASE_SERVICE_ROLE_KEY !== 'REPLACE_WITH_YOUR_SERVICE_ROLE_KEY';

// Log which database we're using
if (typeof window === 'undefined') {
  if (useSupabase) {
    console.log('🚀 Using Supabase database');
  } else {
    console.log('📱 Using SQLite database (Supabase not fully configured)');
  }
}

let databaseModule: any;

if (useSupabase) {
  databaseModule = require('./database-supabase');
} else {
  databaseModule = require('./database-sqlite-backup');
}

export const initDatabase = databaseModule.initDatabase;
export const getDatabase = databaseModule.getDatabase;
export const closeDatabase = databaseModule.closeDatabase;
export const runMigrations = databaseModule.runMigrations;
