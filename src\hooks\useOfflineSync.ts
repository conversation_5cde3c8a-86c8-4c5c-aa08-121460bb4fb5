'use client';

import { useState, useEffect, useCallback } from 'react';
import { getDatabaseStatus } from '@/lib/database-hybrid';
import { syncAllData, getSyncStatus, setAutoSync, SyncResult, SyncStatus } from '@/lib/sync-service';

export interface UseOfflineSyncReturn {
  // Status
  isOnline: boolean;
  isSupabaseMode: boolean;
  isSQLiteMode: boolean;
  syncStatus: SyncStatus | null;
  
  // Actions
  sync: () => Promise<SyncResult>;
  toggleAutoSync: () => void;
  
  // State
  isSyncing: boolean;
  lastSyncResult: SyncResult | null;
  error: string | null;
}

export const useOfflineSync = (): UseOfflineSyncReturn => {
  const [isOnline, setIsOnline] = useState(true);
  const [isSupabaseMode, setIsSupabaseMode] = useState(false);
  const [isSQLiteMode, setIsSQLiteMode] = useState(true);
  const [syncStatus, setSyncStatus] = useState<SyncStatus | null>(null);
  const [isSyncing, setIsSyncing] = useState(false);
  const [lastSyncResult, setLastSyncResult] = useState<SyncResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Update database status
  const updateStatus = useCallback(async () => {
    try {
      const [dbStatus, syncStat] = await Promise.all([
        getDatabaseStatus(),
        getSyncStatus()
      ]);
      
      setIsOnline(dbStatus.isOnline);
      setIsSupabaseMode(dbStatus.usingSupabase);
      setIsSQLiteMode(dbStatus.usingSQLite);
      setSyncStatus(syncStat);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    }
  }, []);

  // Manual sync function
  const sync = useCallback(async (): Promise<SyncResult> => {
    setIsSyncing(true);
    setError(null);
    
    try {
      const result = await syncAllData();
      setLastSyncResult(result);
      
      if (!result.success) {
        setError(result.errors.join(', '));
      }
      
      // Update status after sync
      await updateStatus();
      
      return result;
    } catch (err) {
      const error = err instanceof Error ? err.message : 'Sync failed';
      setError(error);
      
      const failedResult: SyncResult = {
        success: false,
        totalSynced: 0,
        errors: [error],
        details: {
          users: { synced: 0, errors: [error] },
          templates: { synced: 0, errors: [error] },
          documents: { synced: 0, errors: [error] }
        }
      };
      
      setLastSyncResult(failedResult);
      return failedResult;
    } finally {
      setIsSyncing(false);
    }
  }, [updateStatus]);

  // Toggle auto-sync
  const toggleAutoSync = useCallback(() => {
    if (syncStatus) {
      const newAutoSyncState = !syncStatus.autoSyncEnabled;
      setAutoSync(newAutoSyncState);
      setSyncStatus(prev => prev ? { ...prev, autoSyncEnabled: newAutoSyncState } : null);
    }
  }, [syncStatus]);

  // Initialize and set up event listeners
  useEffect(() => {
    updateStatus();
    
    // Set up periodic status updates
    const interval = setInterval(updateStatus, 30000); // Every 30 seconds
    
    // Browser online/offline event listeners
    const handleOnline = () => {
      setIsOnline(true);
      updateStatus();
    };
    
    const handleOffline = () => {
      setIsOnline(false);
      setIsSupabaseMode(false);
      setIsSQLiteMode(true);
    };
    
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    
    return () => {
      clearInterval(interval);
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [updateStatus]);

  return {
    // Status
    isOnline,
    isSupabaseMode,
    isSQLiteMode,
    syncStatus,
    
    // Actions
    sync,
    toggleAutoSync,
    
    // State
    isSyncing,
    lastSyncResult,
    error
  };
};
