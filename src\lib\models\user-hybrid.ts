// Hybrid user model - automatically switches between Supabase and SQLite based on connectivity

import { getDatabaseStatus } from '../database-hybrid';

// Dynamic imports for model implementations
let supabaseUserModel: any;
let sqliteUserModel: any;

// Initialize model modules
const initModelModules = () => {
  if (!supabaseUserModel) {
    supabaseUserModel = require('./user-supabase');
  }
  if (!sqliteUserModel) {
    sqliteUserModel = require('./user-sqlite-backup');
  }
};

// Get the appropriate model implementation
const getUserModelImpl = async () => {
  initModelModules();
  
  const status = await getDatabaseStatus();
  return status.usingSupabase ? supabaseUserModel : sqliteUserModel;
};

// Re-export types (same for both implementations)
export type User = {
  id?: number;
  username: string;
  passwordHash: string;
  recoveryOptions: {
    privateKey?: string;
    securityQuestions?: {
      question: string;
      answerHash: string;
    }[];
  };
  createdAt?: string;
  updatedAt?: string;
};

export type LoginCredentials = {
  username: string;
  password: string;
};

export type SignupData = {
  username: string;
  password: string;
  recoveryMethod: 'privateKey' | 'securityQuestions';
  privateKey?: string;
  securityQuestions?: {
    question: string;
    answer: string;
  }[];
};

export type RecoveryData = {
  username: string;
  recoveryMethod: 'privateKey' | 'securityQuestions';
  privateKey?: string;
  securityAnswers?: string[];
};

// Hybrid UserModel class
export class UserModel {
  static async findByUsername(username: string): Promise<User | null> {
    const impl = await getUserModelImpl();
    return impl.UserModel.findByUsername(username);
  }

  static async create(userData: Omit<User, 'id' | 'createdAt' | 'updatedAt'>): Promise<User> {
    const impl = await getUserModelImpl();
    return impl.UserModel.create(userData);
  }

  static async updatePassword(username: string, newPasswordHash: string): Promise<boolean> {
    const impl = await getUserModelImpl();
    return impl.UserModel.updatePassword(username, newPasswordHash);
  }

  static async getAllUsers(): Promise<User[]> {
    const impl = await getUserModelImpl();
    return impl.UserModel.getAllUsers();
  }

  static async deleteUser(username: string): Promise<boolean> {
    const impl = await getUserModelImpl();
    return impl.UserModel.deleteUser(username);
  }
}

// Hybrid authentication functions
export const signup = async (signupData: SignupData): Promise<{ success: boolean; message: string; user?: User }> => {
  const impl = await getUserModelImpl();
  return impl.signup(signupData);
};

export const login = async (credentials: LoginCredentials): Promise<{ success: boolean; message: string; user?: User }> => {
  const impl = await getUserModelImpl();
  return impl.login(credentials);
};

export const recoverAccount = async (recoveryData: RecoveryData): Promise<{ success: boolean; message: string; newPassword?: string }> => {
  const impl = await getUserModelImpl();
  return impl.recoverAccount(recoveryData);
};

// Utility functions
export const hashPassword = async (password: string): Promise<string> => {
  const impl = await getUserModelImpl();
  return impl.hashPassword(password);
};

export const generatePrivateKey = async (): Promise<string> => {
  const impl = await getUserModelImpl();
  return impl.generatePrivateKey();
};

export const encrypt = async (text: string): Promise<string> => {
  const impl = await getUserModelImpl();
  return impl.encrypt(text);
};

export const decrypt = async (ciphertext: string): Promise<string> => {
  const impl = await getUserModelImpl();
  return impl.decrypt(ciphertext);
};

// Constants
export const SECURITY_QUESTIONS = [
  "What was the name of your childhood best friend?",
  "What is the name of the street you grew up on?",
  "What was the name of your first pet?",
  "What was your favorite subject in school?",
  "What is the middle name of your oldest sibling?"
];

// Sync function for offline changes
export const syncUserChanges = async (): Promise<{
  success: boolean;
  synced: number;
  errors: any[];
}> => {
  // This would implement logic to sync offline user changes to online database
  // For now, it's a placeholder
  console.log('🔄 User sync functionality would be implemented here');
  
  return { success: true, synced: 0, errors: [] };
};
