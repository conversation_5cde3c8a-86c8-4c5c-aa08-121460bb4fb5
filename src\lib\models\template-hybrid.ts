// Hybrid template model - automatically switches between Supabase and SQLite based on connectivity

import { getDatabaseStatus } from '../database-hybrid';
import { Template } from '@/types/template';

// Dynamic imports for model implementations
let supabaseTemplateModel: any;
let sqliteTemplateModel: any;

// Initialize model modules
const initModelModules = () => {
  if (!supabaseTemplateModel) {
    supabaseTemplateModel = require('./template-supabase');
  }
  if (!sqliteTemplateModel) {
    sqliteTemplateModel = require('./template-sqlite-backup');
  }
};

// Get the appropriate model implementation
const getTemplateModelImpl = async () => {
  initModelModules();
  
  const status = await getDatabaseStatus();
  return status.usingSupabase ? supabaseTemplateModel : sqliteTemplateModel;
};

// Hybrid TemplateModel class
export class TemplateModel {
  static async findById(id: string): Promise<Template | null> {
    const impl = await getTemplateModelImpl();
    return impl.TemplateModel.findById(id);
  }

  static async findAll(): Promise<Template[]> {
    const impl = await getTemplateModelImpl();
    return impl.TemplateModel.findAll();
  }

  static async create(template: Omit<Template, 'uploadedAt'>): Promise<Template> {
    const impl = await getTemplateModelImpl();
    return impl.TemplateModel.create(template);
  }

  static async update(id: string, updates: Partial<Omit<Template, 'id' | 'uploadedAt'>>): Promise<Template | null> {
    const impl = await getTemplateModelImpl();
    return impl.TemplateModel.update(id, updates);
  }

  static async delete(id: string): Promise<boolean> {
    const impl = await getTemplateModelImpl();
    return impl.TemplateModel.delete(id);
  }

  static async search(query: string): Promise<Template[]> {
    const impl = await getTemplateModelImpl();
    return impl.TemplateModel.search(query);
  }

  static async getTemplateStats(): Promise<{
    totalTemplates: number;
    templatesThisWeek: number;
    templatesThisMonth: number;
  }> {
    const impl = await getTemplateModelImpl();
    return impl.TemplateModel.getTemplateStats();
  }
}

// Document Record interface
export interface DocumentRecord {
  id?: number;
  templateId: string;
  documentData: Record<string, any>;
  generatedAt?: string;
}

// Hybrid DocumentModel class
export class DocumentModel {
  static async create(document: Omit<DocumentRecord, 'id' | 'generatedAt'>): Promise<DocumentRecord> {
    const impl = await getTemplateModelImpl();
    return impl.DocumentModel.create(document);
  }

  static async findByTemplateId(templateId: string, limit?: number): Promise<DocumentRecord[]> {
    const impl = await getTemplateModelImpl();
    return impl.DocumentModel.findByTemplateId(templateId, limit);
  }

  static async findAll(limit?: number): Promise<DocumentRecord[]> {
    const impl = await getTemplateModelImpl();
    return impl.DocumentModel.findAll(limit);
  }

  static async delete(id: number): Promise<boolean> {
    const impl = await getTemplateModelImpl();
    return impl.DocumentModel.delete(id);
  }

  static async getDocumentStats(): Promise<{
    totalDocuments: number;
    documentsToday: number;
    documentsThisWeek: number;
    documentsThisMonth: number;
  }> {
    const impl = await getTemplateModelImpl();
    return impl.DocumentModel.getDocumentStats();
  }
}

// Sync functions for offline changes
export const syncTemplateChanges = async (): Promise<{
  success: boolean;
  synced: number;
  errors: any[];
}> => {
  // This would implement logic to sync offline template changes to online database
  console.log('🔄 Template sync functionality would be implemented here');
  
  return { success: true, synced: 0, errors: [] };
};

export const syncDocumentChanges = async (): Promise<{
  success: boolean;
  synced: number;
  errors: any[];
}> => {
  // This would implement logic to sync offline document changes to online database
  console.log('🔄 Document sync functionality would be implemented here');
  
  return { success: true, synced: 0, errors: [] };
};
