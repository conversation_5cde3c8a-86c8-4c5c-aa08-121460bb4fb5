// Template model abstraction layer - environment-based switching
// Uses Supabase if configured, otherwise falls back to SQLite

const useSupabase = process.env.NEXT_PUBLIC_SUPABASE_URL &&
                   process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY &&
                   process.env.SUPABASE_SERVICE_ROLE_KEY !== 'REPLACE_WITH_YOUR_SERVICE_ROLE_KEY';

let templateModule: any;

if (useSupabase) {
  templateModule = require('./template-supabase');
} else {
  templateModule = require('./template-sqlite-backup');
}

export const TemplateModel = templateModule.TemplateModel;
export const DocumentModel = templateModule.DocumentModel;
