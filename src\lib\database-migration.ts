// Migration utility to switch from SQLite to Supabase
// This file helps with the transition and can be removed after migration

import { supabaseAdmin } from './supabase';

// Re-export Supabase models with the same interface as SQLite models
export { UserModel, signup, login, recoverAccount, hashPassword, generatePrivateKey, encrypt, decrypt, SECURITY_QUESTIONS } from './models/user-supabase';
export { TemplateModel, DocumentModel } from './models/template-supabase';

// Re-export database functions with Supabase implementation
export { initDatabase, getDatabase, closeDatabase, runMigrations } from './database-supabase';

// Migration helper functions
export const migrateFromSQLite = async () => {
  console.log('Starting migration from SQLite to Supabase...');
  
  try {
    // Initialize Supabase connection
    await initDatabase();
    
    // Test connection
    const { data, error } = await supabaseAdmin.from('users').select('count').limit(1);
    if (error) {
      throw new Error(`Supabase connection failed: ${error.message}`);
    }
    
    console.log('✅ Supabase connection established');
    console.log('✅ Migration setup complete');
    console.log('');
    console.log('Next steps:');
    console.log('1. Run the SQL schema in your Supabase dashboard (supabase-schema.sql)');
    console.log('2. Update your environment variables (.env.local)');
    console.log('3. Test the application with Supabase');
    console.log('4. Remove SQLite dependencies when ready');
    
    return { success: true, message: 'Migration setup complete' };
  } catch (error) {
    console.error('❌ Migration failed:', error);
    return { success: false, message: `Migration failed: ${error}` };
  }
};

// Utility to check if Supabase is properly configured
export const checkSupabaseConfig = () => {
  const requiredEnvVars = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    'SUPABASE_SERVICE_ROLE_KEY'
  ];
  
  const missing = requiredEnvVars.filter(envVar => !process.env[envVar]);
  
  if (missing.length > 0) {
    console.error('❌ Missing required environment variables:', missing);
    console.log('Please add these to your .env.local file:');
    missing.forEach(envVar => {
      console.log(`${envVar}=your_value_here`);
    });
    return false;
  }
  
  console.log('✅ All required environment variables are set');
  return true;
};

// Export the migration function for use in API routes or scripts
export default migrateFromSQLite;
