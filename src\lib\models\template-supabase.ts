import { supabaseAdmin } from '../supabase';
import { handleSupabaseError, convertDateFilter } from '../database-supabase';
import { Template } from '@/types/template';

/* eslint-disable @typescript-eslint/no-explicit-any */

export class TemplateModel {
  static async findById(id: string): Promise<Template | null> {
    try {
      const { data, error } = await supabaseAdmin
        .from('templates')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return null; // No template found
        }
        handleSupabaseError(error, 'findById');
      }

      if (!data) return null;

      return {
        id: data.id,
        name: data.name,
        description: data.description,
        filename: data.filename,
        placeholders: JSON.parse(data.placeholders || '[]'),
        layoutSize: data.layout_size as 'A4' | 'Letter',
        hasApplicantPhoto: <PERSON><PERSON><PERSON>(data.has_applicant_photo),
        uploadedAt: data.uploaded_at,
      };
    } catch (error) {
      console.error('Error finding template by ID:', error);
      throw error;
    }
  }

  static async findAll(): Promise<Template[]> {
    try {
      const { data, error } = await supabaseAdmin
        .from('templates')
        .select('*')
        .order('uploaded_at', { ascending: false });

      if (error) {
        handleSupabaseError(error, 'findAll');
      }

      return (data || []).map(row => ({
        id: row.id,
        name: row.name,
        description: row.description,
        filename: row.filename,
        placeholders: JSON.parse(row.placeholders || '[]'),
        layoutSize: row.layout_size as 'A4' | 'Letter',
        hasApplicantPhoto: Boolean(row.has_applicant_photo),
        uploadedAt: row.uploaded_at,
      }));
    } catch (error) {
      console.error('Error finding all templates:', error);
      throw error;
    }
  }

  static async create(template: Omit<Template, 'uploadedAt'>): Promise<Template> {
    try {
      const { data, error } = await supabaseAdmin
        .from('templates')
        .insert({
          id: template.id,
          name: template.name,
          description: template.description,
          filename: template.filename,
          placeholders: JSON.stringify(template.placeholders),
          layout_size: template.layoutSize,
          has_applicant_photo: template.hasApplicantPhoto || false
        })
        .select()
        .single();

      if (error) {
        handleSupabaseError(error, 'create template');
      }

      return {
        id: data.id,
        name: data.name,
        description: data.description,
        filename: data.filename,
        placeholders: JSON.parse(data.placeholders || '[]'),
        layoutSize: data.layout_size as 'A4' | 'Letter',
        hasApplicantPhoto: Boolean(data.has_applicant_photo),
        uploadedAt: data.uploaded_at,
      };
    } catch (error) {
      console.error('Error creating template:', error);
      throw error;
    }
  }

  static async update(id: string, updates: Partial<Omit<Template, 'id' | 'uploadedAt'>>): Promise<Template | null> {
    try {
      const updateData: any = {};
      
      if (updates.name !== undefined) updateData.name = updates.name;
      if (updates.description !== undefined) updateData.description = updates.description;
      if (updates.filename !== undefined) updateData.filename = updates.filename;
      if (updates.placeholders !== undefined) updateData.placeholders = JSON.stringify(updates.placeholders);
      if (updates.layoutSize !== undefined) updateData.layout_size = updates.layoutSize;
      if (updates.hasApplicantPhoto !== undefined) updateData.has_applicant_photo = updates.hasApplicantPhoto;

      const { data, error } = await supabaseAdmin
        .from('templates')
        .update(updateData)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return null; // No template found
        }
        handleSupabaseError(error, 'update template');
      }

      return {
        id: data.id,
        name: data.name,
        description: data.description,
        filename: data.filename,
        placeholders: JSON.parse(data.placeholders || '[]'),
        layoutSize: data.layout_size as 'A4' | 'Letter',
        hasApplicantPhoto: Boolean(data.has_applicant_photo),
        uploadedAt: data.uploaded_at,
      };
    } catch (error) {
      console.error('Error updating template:', error);
      throw error;
    }
  }

  static async delete(id: string): Promise<boolean> {
    try {
      const { error } = await supabaseAdmin
        .from('templates')
        .delete()
        .eq('id', id);

      if (error) {
        handleSupabaseError(error, 'delete template');
      }

      return true;
    } catch (error) {
      console.error('Error deleting template:', error);
      return false;
    }
  }

  static async search(query: string): Promise<Template[]> {
    try {
      const { data, error } = await supabaseAdmin
        .from('templates')
        .select('*')
        .or(`name.ilike.%${query}%,description.ilike.%${query}%`)
        .order('uploaded_at', { ascending: false });

      if (error) {
        handleSupabaseError(error, 'search templates');
      }

      return (data || []).map(row => ({
        id: row.id,
        name: row.name,
        description: row.description,
        filename: row.filename,
        placeholders: JSON.parse(row.placeholders || '[]'),
        layoutSize: row.layout_size as 'A4' | 'Letter',
        hasApplicantPhoto: Boolean(row.has_applicant_photo),
        uploadedAt: row.uploaded_at,
      }));
    } catch (error) {
      console.error('Error searching templates:', error);
      throw error;
    }
  }

  static async getTemplateStats(): Promise<{
    totalTemplates: number;
    templatesThisWeek: number;
    templatesThisMonth: number;
  }> {
    try {
      // Get total count
      const { count: totalCount, error: totalError } = await supabaseAdmin
        .from('templates')
        .select('*', { count: 'exact', head: true });

      if (totalError) {
        handleSupabaseError(totalError, 'get total template count');
      }

      // Get templates from this week
      const { count: weekCount, error: weekError } = await supabaseAdmin
        .from('templates')
        .select('*', { count: 'exact', head: true })
        .gte('uploaded_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString());

      if (weekError) {
        handleSupabaseError(weekError, 'get weekly template count');
      }

      // Get templates from this month
      const { count: monthCount, error: monthError } = await supabaseAdmin
        .from('templates')
        .select('*', { count: 'exact', head: true })
        .gte('uploaded_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString());

      if (monthError) {
        handleSupabaseError(monthError, 'get monthly template count');
      }

      return {
        totalTemplates: totalCount || 0,
        templatesThisWeek: weekCount || 0,
        templatesThisMonth: monthCount || 0,
      };
    } catch (error) {
      console.error('Error getting template stats:', error);
      throw error;
    }
  }
}

// Document generation tracking
export interface DocumentRecord {
  id?: number;
  templateId: string;
  documentData: Record<string, any>;
  generatedAt?: string;
}

export class DocumentModel {
  static async create(document: Omit<DocumentRecord, 'id' | 'generatedAt'>): Promise<DocumentRecord> {
    try {
      const { data, error } = await supabaseAdmin
        .from('documents')
        .insert({
          template_id: document.templateId,
          document_data: JSON.stringify(document.documentData)
        })
        .select()
        .single();

      if (error) {
        handleSupabaseError(error, 'create document');
      }

      return {
        id: data.id,
        templateId: data.template_id,
        documentData: JSON.parse(data.document_data || '{}'),
        generatedAt: data.generated_at
      };
    } catch (error) {
      console.error('Error creating document:', error);
      throw error;
    }
  }

  static async findByTemplateId(templateId: string, limit?: number): Promise<DocumentRecord[]> {
    try {
      let query = supabaseAdmin
        .from('documents')
        .select('*')
        .eq('template_id', templateId)
        .order('generated_at', { ascending: false });

      if (limit) {
        query = query.limit(limit);
      }

      const { data, error } = await query;

      if (error) {
        handleSupabaseError(error, 'find documents by template ID');
      }

      return (data || []).map(row => ({
        id: row.id,
        templateId: row.template_id,
        documentData: JSON.parse(row.document_data || '{}'),
        generatedAt: row.generated_at
      }));
    } catch (error) {
      console.error('Error finding documents by template ID:', error);
      throw error;
    }
  }

  static async findAll(limit?: number): Promise<DocumentRecord[]> {
    try {
      let query = supabaseAdmin
        .from('documents')
        .select('*')
        .order('generated_at', { ascending: false });

      if (limit) {
        query = query.limit(limit);
      }

      const { data, error } = await query;

      if (error) {
        handleSupabaseError(error, 'find all documents');
      }

      return (data || []).map(row => ({
        id: row.id,
        templateId: row.template_id,
        documentData: JSON.parse(row.document_data || '{}'),
        generatedAt: row.generated_at
      }));
    } catch (error) {
      console.error('Error finding all documents:', error);
      throw error;
    }
  }

  static async delete(id: number): Promise<boolean> {
    try {
      const { error } = await supabaseAdmin
        .from('documents')
        .delete()
        .eq('id', id);

      if (error) {
        handleSupabaseError(error, 'delete document');
      }

      return true;
    } catch (error) {
      console.error('Error deleting document:', error);
      return false;
    }
  }

  static async getDocumentStats(): Promise<{
    totalDocuments: number;
    documentsToday: number;
    documentsThisWeek: number;
    documentsThisMonth: number;
  }> {
    try {
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate()).toISOString();
      const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString();
      const monthAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString();

      // Get total count
      const { count: totalCount, error: totalError } = await supabaseAdmin
        .from('documents')
        .select('*', { count: 'exact', head: true });

      if (totalError) {
        handleSupabaseError(totalError, 'get total document count');
      }

      // Get documents from today
      const { count: todayCount, error: todayError } = await supabaseAdmin
        .from('documents')
        .select('*', { count: 'exact', head: true })
        .gte('generated_at', today);

      if (todayError) {
        handleSupabaseError(todayError, 'get daily document count');
      }

      // Get documents from this week
      const { count: weekCount, error: weekError } = await supabaseAdmin
        .from('documents')
        .select('*', { count: 'exact', head: true })
        .gte('generated_at', weekAgo);

      if (weekError) {
        handleSupabaseError(weekError, 'get weekly document count');
      }

      // Get documents from this month
      const { count: monthCount, error: monthError } = await supabaseAdmin
        .from('documents')
        .select('*', { count: 'exact', head: true })
        .gte('generated_at', monthAgo);

      if (monthError) {
        handleSupabaseError(monthError, 'get monthly document count');
      }

      return {
        totalDocuments: totalCount || 0,
        documentsToday: todayCount || 0,
        documentsThisWeek: weekCount || 0,
        documentsThisMonth: monthCount || 0,
      };
    } catch (error) {
      console.error('Error getting document stats:', error);
      throw error;
    }
  }
}
