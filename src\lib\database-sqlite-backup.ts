// SQLite database implementation (backup/offline mode)
// Server-side only imports
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-require-imports */
let Database: any;
let path: any;
let fs: any;

// Only import on server side
if (typeof window === 'undefined') {
  Database = require('better-sqlite3');
  path = require('path');
  fs = require('fs').promises;
}

// Database file path
const DB_PATH = typeof window === 'undefined' ? path?.join(process.cwd(), 'data', 'ldis.db') : '';

let db: any = null;

// Ensure data directory exists
const ensureDataDir = async (): Promise<void> => {
  const dataDir = path.dirname(DB_PATH);
  try {
    await fs.access(dataDir);
  } catch {
    await fs.mkdir(dataDir, { recursive: true });
  }
};

// Initialize database connection
export const initDatabase = async (): Promise<any> => {
  // Only run on server side
  if (typeof window !== 'undefined') {
    throw new Error('Database operations can only be performed on the server side');
  }

  if (db) {
    return db;
  }

  await ensureDataDir();

  db = new Database(DB_PATH);

  // Enable WAL mode for better performance
  db.pragma('journal_mode = WAL');

  // Create tables if they don't exist
  await createTables();

  return db;
};

// Get database instance
export const getDatabase = async (): Promise<any> => {
  if (typeof window !== 'undefined') {
    throw new Error('Database operations can only be performed on the server side');
  }

  if (!db) {
    return await initDatabase();
  }
  return db;
};

// Create database tables
const createTables = async (): Promise<void> => {
  if (!db) return;

  // Users table
  db.exec(`
    CREATE TABLE IF NOT EXISTS users (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      username TEXT UNIQUE NOT NULL,
      password_hash TEXT NOT NULL,
      recovery_options TEXT, -- JSON string
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Templates table
  db.exec(`
    CREATE TABLE IF NOT EXISTS templates (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      description TEXT,
      filename TEXT NOT NULL,
      placeholders TEXT, -- JSON array
      layout_size TEXT DEFAULT 'A4',
      has_applicant_photo BOOLEAN DEFAULT FALSE,
      uploaded_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Documents table (for generated documents)
  db.exec(`
    CREATE TABLE IF NOT EXISTS documents (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      template_id TEXT NOT NULL,
      document_data TEXT, -- JSON string of form data
      generated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (template_id) REFERENCES templates (id)
    )
  `);

  // Create indexes for better performance
  db.exec(`
    CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
    CREATE INDEX IF NOT EXISTS idx_templates_name ON templates(name);
    CREATE INDEX IF NOT EXISTS idx_documents_template_id ON documents(template_id);
    CREATE INDEX IF NOT EXISTS idx_documents_generated_at ON documents(generated_at);
  `);
};

// Close database connection
export const closeDatabase = (): void => {
  if (db) {
    db.close();
    db = null;
  }
};

// Utility function to run migrations
export const runMigrations = async (): Promise<void> => {
  const database = await getDatabase();
  
  // Create migrations table if it doesn't exist
  database.exec(`
    CREATE TABLE IF NOT EXISTS migrations (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT UNIQUE NOT NULL,
      executed_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Add any future migrations here
  const migrations: Array<{name: string; sql: string}> = [
    // Example migration structure:
    // {
    //   name: '001_add_user_roles',
    //   sql: 'ALTER TABLE users ADD COLUMN role TEXT DEFAULT "user"'
    // }
  ];

  for (const migration of migrations) {
    const existing = database.prepare('SELECT name FROM migrations WHERE name = ?').get(migration.name);
    
    if (!existing) {
      database.exec(migration.sql);
      database.prepare('INSERT INTO migrations (name) VALUES (?)').run(migration.name);
      console.log(`Migration ${migration.name} executed successfully`);
    }
  }
};
