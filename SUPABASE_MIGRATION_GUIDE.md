# Supabase Migration Guide

This guide will help you migrate your LDIS application from SQLite to Supabase.

## Prerequisites

1. A Supabase account and project
2. Your Supabase project URL and API keys

## Step 1: Set up Supabase Project

1. Go to [Supabase Dashboard](https://supabase.com/dashboard)
2. Create a new project or use an existing one
3. Note down your project URL and API keys from Settings > API

## Step 2: Run Database Schema

1. Go to your Supabase project dashboard
2. Navigate to SQL Editor
3. Copy and paste the contents of `supabase-schema.sql`
4. Run the SQL script to create all tables and policies

## Step 3: Configure Environment Variables

1. Copy `.env.local.example` to `.env.local`
2. Fill in your Supabase credentials:

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
```

## Step 4: Test the Migration

1. Start your development server: `pnpm dev`
2. Check the console for "🚀 Using Supabase database" message
3. Test user authentication (signup/login)
4. Test template operations (create/read/update/delete)
5. Test document generation

## Step 5: Data Migration (Optional)

If you have existing SQLite data you want to migrate:

1. Export your SQLite data
2. Use the Supabase dashboard or API to import the data
3. Ensure all foreign key relationships are maintained

## Step 6: Remove SQLite Dependencies (After Testing)

Once you've confirmed everything works with Supabase:

1. Remove SQLite from package.json: `pnpm remove better-sqlite3 @types/better-sqlite3`
2. Delete SQLite backup files:
   - `src/lib/database-sqlite-backup.ts`
   - `src/lib/models/user-sqlite-backup.ts`
   - `src/lib/models/template-sqlite-backup.ts`
3. Remove the `data/` directory with SQLite files
4. Update `.gitignore` to remove SQLite-related entries

## Rollback Plan

If you need to rollback to SQLite:

1. Remove or comment out Supabase environment variables
2. The application will automatically fallback to SQLite
3. Your original SQLite files are preserved in backup files

## Key Differences

### SQLite vs Supabase

| Feature | SQLite | Supabase |
|---------|--------|----------|
| Database Type | File-based | PostgreSQL |
| Scaling | Single instance | Cloud-based |
| Authentication | Custom | Built-in Auth |
| Real-time | No | Yes |
| Backups | Manual | Automatic |

### API Changes

The API remains the same, but internally:
- SQLite prepared statements → Supabase queries
- Local file operations → Cloud database operations
- Manual migrations → Supabase migrations

## Troubleshooting

### Common Issues

1. **Environment variables not loaded**: Restart your development server
2. **Connection errors**: Check your Supabase URL and keys
3. **Schema errors**: Ensure you ran the complete SQL schema
4. **Permission errors**: Check your RLS policies in Supabase

### Getting Help

1. Check Supabase documentation: https://supabase.com/docs
2. Review the migration files in `src/lib/`
3. Check the console for detailed error messages

## Performance Considerations

- Supabase queries are network-based (vs local SQLite)
- Consider implementing caching for frequently accessed data
- Use Supabase's built-in caching and CDN features
- Monitor query performance in Supabase dashboard

## Security Notes

- Never expose your service role key in client-side code
- Use Row Level Security (RLS) policies for data protection
- Regularly rotate your API keys
- Monitor access logs in Supabase dashboard
