/** @type {import('next').NextConfig} */
const nextConfig = {
  webpack: (config, { isServer }) => {
    // Client-side fallbacks for Node.js modules
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        path: false,
        crypto: false,
      };
    }

    return config;
  },
  // Remove SQLite-specific external packages since we're using Supabase
  // serverExternalPackages: ["better-sqlite3"], // Commented out for Supabase migration
};

module.exports = nextConfig;
