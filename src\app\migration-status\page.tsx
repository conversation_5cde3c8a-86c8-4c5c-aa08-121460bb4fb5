'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';

interface TestResult {
  success: boolean;
  message: string;
  performanceMs?: number;
  timestamp?: string;
  error?: any;
}

export default function MigrationStatusPage() {
  const [testResult, setTestResult] = useState<TestResult | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const runMigrationTest = async () => {
    setIsLoading(true);
    setTestResult(null);

    try {
      const response = await fetch('/api/test-migration');
      const result = await response.json();
      setTestResult(result);
    } catch (error) {
      setTestResult({
        success: false,
        message: 'Failed to run migration test',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const checkDatabaseType = () => {
    const hasSupabaseEnv = process.env.NEXT_PUBLIC_SUPABASE_URL && process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
    return hasSupabaseEnv ? 'Supabase' : 'SQLite (Fallback)';
  };

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="space-y-6">
        <div className="text-center">
          <h1 className="text-3xl font-bold mb-2">Database Migration Status</h1>
          <p className="text-gray-600">
            Check the status of your SQLite to Supabase migration
          </p>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4">Current Database</h2>
          <div className="flex items-center space-x-2">
            <div className={`w-3 h-3 rounded-full ${
              checkDatabaseType() === 'Supabase' ? 'bg-green-500' : 'bg-yellow-500'
            }`}></div>
            <span className="font-medium">{checkDatabaseType()}</span>
          </div>
          
          {checkDatabaseType() === 'SQLite (Fallback)' && (
            <div className="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded">
              <p className="text-sm text-yellow-800">
                ⚠️ You're currently using SQLite. To use Supabase, please set up your environment variables.
              </p>
            </div>
          )}
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4">Migration Test</h2>
          <p className="text-gray-600 mb-4">
            Run a comprehensive test to validate your database integration.
          </p>
          
          <Button 
            onClick={runMigrationTest} 
            disabled={isLoading}
            className="mb-4"
          >
            {isLoading ? 'Running Tests...' : 'Run Migration Test'}
          </Button>

          {testResult && (
            <div className={`p-4 rounded-lg border ${
              testResult.success 
                ? 'bg-green-50 border-green-200' 
                : 'bg-red-50 border-red-200'
            }`}>
              <div className="flex items-center space-x-2 mb-2">
                <div className={`w-3 h-3 rounded-full ${
                  testResult.success ? 'bg-green-500' : 'bg-red-500'
                }`}></div>
                <span className={`font-medium ${
                  testResult.success ? 'text-green-800' : 'text-red-800'
                }`}>
                  {testResult.success ? 'All Tests Passed' : 'Tests Failed'}
                </span>
              </div>
              
              <p className={`text-sm ${
                testResult.success ? 'text-green-700' : 'text-red-700'
              }`}>
                {testResult.message}
              </p>
              
              {testResult.performanceMs && (
                <p className="text-sm text-gray-600 mt-1">
                  Performance: {testResult.performanceMs}ms
                </p>
              )}
              
              {testResult.timestamp && (
                <p className="text-xs text-gray-500 mt-2">
                  Tested at: {new Date(testResult.timestamp).toLocaleString()}
                </p>
              )}
              
              {!testResult.success && testResult.error && (
                <details className="mt-3">
                  <summary className="cursor-pointer text-sm font-medium text-red-800">
                    Error Details
                  </summary>
                  <pre className="mt-2 text-xs bg-red-100 p-2 rounded overflow-auto">
                    {JSON.stringify(testResult.error, null, 2)}
                  </pre>
                </details>
              )}
            </div>
          )}
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4">Migration Checklist</h2>
          <div className="space-y-3">
            <div className="flex items-center space-x-3">
              <input type="checkbox" className="rounded" />
              <span className="text-sm">Set up Supabase project</span>
            </div>
            <div className="flex items-center space-x-3">
              <input type="checkbox" className="rounded" />
              <span className="text-sm">Run SQL schema in Supabase dashboard</span>
            </div>
            <div className="flex items-center space-x-3">
              <input type="checkbox" className="rounded" />
              <span className="text-sm">Configure environment variables</span>
            </div>
            <div className="flex items-center space-x-3">
              <input type="checkbox" className="rounded" />
              <span className="text-sm">Run migration tests</span>
            </div>
            <div className="flex items-center space-x-3">
              <input type="checkbox" className="rounded" />
              <span className="text-sm">Test application functionality</span>
            </div>
            <div className="flex items-center space-x-3">
              <input type="checkbox" className="rounded" />
              <span className="text-sm">Remove SQLite dependencies (optional)</span>
            </div>
          </div>
        </div>

        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="font-medium text-blue-800 mb-2">Need Help?</h3>
          <p className="text-sm text-blue-700">
            Check the <code>SUPABASE_MIGRATION_GUIDE.md</code> file for detailed instructions
            and troubleshooting tips.
          </p>
        </div>
      </div>
    </div>
  );
}
