// User model abstraction layer - environment-based switching
// Uses Supabase if configured, otherwise falls back to SQLite

const useSupabase = process.env.NEXT_PUBLIC_SUPABASE_URL &&
                   process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY &&
                   process.env.SUPABASE_SERVICE_ROLE_KEY !== 'REPLACE_WITH_YOUR_SERVICE_ROLE_KEY';

let userModule: any;

if (useSupabase) {
  userModule = require('./user-supabase');
} else {
  userModule = require('./user-sqlite-backup');
}

export const UserModel = userModule.UserModel;
export const signup = userModule.signup;
export const login = userModule.login;
export const recoverAccount = userModule.recoverAccount;
export const hashPassword = userModule.hashPassword;
export const generatePrivateKey = userModule.generatePrivateKey;
export const encrypt = userModule.encrypt;
export const decrypt = userModule.decrypt;
export const SECURITY_QUESTIONS = userModule.SECURITY_QUESTIONS;

export type User = userModule.User;
export type LoginCredentials = userModule.LoginCredentials;
export type SignupData = userModule.SignupData;
export type RecoveryData = userModule.RecoveryData;
