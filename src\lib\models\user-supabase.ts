import { supabaseAdmin } from '../supabase';
import { handleSupabaseError } from '../database-supabase';

// Server-side only imports
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-require-imports */
let CryptoJS: any;
if (typeof window === 'undefined') {
  CryptoJS = require('crypto-js');
}

// Secret key for encryption (in production, this should be from environment variables)
const SECRET_KEY = process.env.LDIS_AUTH_SECRET_2024 || 'LDIS_AUTH_SECRET_2024';

export interface User {
  id?: number;
  username: string;
  passwordHash: string;
  recoveryOptions: {
    privateKey?: string;
    securityQuestions?: {
      question: string;
      answerHash: string;
    }[];
  };
  createdAt?: string;
  updatedAt?: string;
}

export interface LoginCredentials {
  username: string;
  password: string;
}

export interface SignupData {
  username: string;
  password: string;
  recoveryMethod: 'privateKey' | 'securityQuestions';
  privateKey?: string;
  securityQuestions?: {
    question: string;
    answer: string;
  }[];
}

export interface RecoveryData {
  username: string;
  recoveryMethod: 'privateKey' | 'securityQuestions';
  privateKey?: string;
  securityAnswers?: string[];
}

// Security questions
export const SECURITY_QUESTIONS = [
  "What was the name of your childhood best friend?",
  "What is the name of the street you grew up on?",
  "What was the name of your first pet?",
  "What was your favorite subject in school?",
  "What is the middle name of your oldest sibling?"
];

// Encryption/Decryption functions
export const encrypt = (text: string): string => {
  if (typeof window !== 'undefined') {
    throw new Error('Encryption can only be performed on the server side');
  }
  return CryptoJS.AES.encrypt(text, SECRET_KEY).toString();
};

export const decrypt = (ciphertext: string): string => {
  if (typeof window !== 'undefined') {
    throw new Error('Decryption can only be performed on the server side');
  }
  const bytes = CryptoJS.AES.decrypt(ciphertext, SECRET_KEY);
  return bytes.toString(CryptoJS.enc.Utf8);
};

// Password hashing
export const hashPassword = (password: string): string => {
  if (typeof window !== 'undefined') {
    throw new Error('Password hashing can only be performed on the server side');
  }
  return CryptoJS.SHA256(password + SECRET_KEY).toString();
};

// Generate private key
export const generatePrivateKey = (): string => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < 32; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
};

// User database operations with Supabase
export class UserModel {
  static async findByUsername(username: string): Promise<User | null> {
    if (typeof window !== 'undefined') {
      throw new Error('Database operations can only be performed on the server side');
    }

    try {
      const { data, error } = await supabaseAdmin
        .from('users')
        .select('*')
        .eq('username', username)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return null; // No user found
        }
        handleSupabaseError(error, 'findByUsername');
      }

      if (!data) return null;

      return {
        id: data.id,
        username: data.username,
        passwordHash: data.password_hash,
        recoveryOptions: JSON.parse(data.recovery_options || '{}'),
        createdAt: data.created_at,
        updatedAt: data.updated_at
      };
    } catch (error) {
      console.error('Error finding user by username:', error);
      throw error;
    }
  }

  static async create(userData: Omit<User, 'id' | 'createdAt' | 'updatedAt'>): Promise<User> {
    if (typeof window !== 'undefined') {
      throw new Error('Database operations can only be performed on the server side');
    }

    try {
      const { data, error } = await supabaseAdmin
        .from('users')
        .insert({
          username: userData.username,
          password_hash: userData.passwordHash,
          recovery_options: JSON.stringify(userData.recoveryOptions)
        })
        .select()
        .single();

      if (error) {
        handleSupabaseError(error, 'create user');
      }

      return {
        id: data.id,
        username: data.username,
        passwordHash: data.password_hash,
        recoveryOptions: JSON.parse(data.recovery_options || '{}'),
        createdAt: data.created_at,
        updatedAt: data.updated_at
      };
    } catch (error) {
      console.error('Error creating user:', error);
      throw error;
    }
  }

  static async updatePassword(username: string, newPasswordHash: string): Promise<boolean> {
    if (typeof window !== 'undefined') {
      throw new Error('Database operations can only be performed on the server side');
    }

    try {
      const { error } = await supabaseAdmin
        .from('users')
        .update({ 
          password_hash: newPasswordHash,
          updated_at: new Date().toISOString()
        })
        .eq('username', username);

      if (error) {
        handleSupabaseError(error, 'update password');
      }

      return true;
    } catch (error) {
      console.error('Error updating password:', error);
      return false;
    }
  }

  static async getAllUsers(): Promise<User[]> {
    try {
      const { data, error } = await supabaseAdmin
        .from('users')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        handleSupabaseError(error, 'get all users');
      }

      return (data || []).map(row => ({
        id: row.id,
        username: row.username,
        passwordHash: row.password_hash,
        recoveryOptions: JSON.parse(row.recovery_options || '{}'),
        createdAt: row.created_at,
        updatedAt: row.updated_at
      }));
    } catch (error) {
      console.error('Error getting all users:', error);
      throw error;
    }
  }

  static async deleteUser(username: string): Promise<boolean> {
    try {
      const { error } = await supabaseAdmin
        .from('users')
        .delete()
        .eq('username', username);

      if (error) {
        handleSupabaseError(error, 'delete user');
      }

      return true;
    } catch (error) {
      console.error('Error deleting user:', error);
      return false;
    }
  }
}

// Authentication functions
export const signup = async (signupData: SignupData): Promise<{ success: boolean; message: string; user?: User }> => {
  if (typeof window !== 'undefined') {
    throw new Error('Signup can only be performed on the server side');
  }

  try {
    // Check if user already exists
    const existingUser = await UserModel.findByUsername(signupData.username);
    if (existingUser) {
      return { success: false, message: 'Username already exists' };
    }

    // Hash password
    const passwordHash = hashPassword(signupData.password);

    // Prepare recovery options
    let recoveryOptions: User['recoveryOptions'] = {};

    if (signupData.recoveryMethod === 'privateKey') {
      if (!signupData.privateKey) {
        return { success: false, message: 'Private key is required for this recovery method' };
      }
      recoveryOptions.privateKey = encrypt(signupData.privateKey);
    } else if (signupData.recoveryMethod === 'securityQuestions') {
      if (!signupData.securityQuestions || signupData.securityQuestions.length === 0) {
        return { success: false, message: 'Security questions are required for this recovery method' };
      }
      recoveryOptions.securityQuestions = signupData.securityQuestions.map(sq => ({
        question: sq.question,
        answerHash: hashPassword(sq.answer.toLowerCase().trim())
      }));
    }

    // Create user
    const user = await UserModel.create({
      username: signupData.username,
      passwordHash,
      recoveryOptions
    });

    return { success: true, message: 'Account created successfully', user };
  } catch (error) {
    console.error('Signup error:', error);
    return { success: false, message: 'Failed to create account' };
  }
};

export const login = async (credentials: LoginCredentials): Promise<{ success: boolean; message: string; user?: User }> => {
  if (typeof window !== 'undefined') {
    throw new Error('Login can only be performed on the server side');
  }

  try {
    const user = await UserModel.findByUsername(credentials.username);
    if (!user) {
      return { success: false, message: 'Invalid username or password' };
    }

    const passwordHash = hashPassword(credentials.password);
    if (user.passwordHash !== passwordHash) {
      return { success: false, message: 'Invalid username or password' };
    }

    return { success: true, message: 'Login successful', user };
  } catch (error) {
    console.error('Login error:', error);
    return { success: false, message: 'Login failed' };
  }
};

export const recoverAccount = async (recoveryData: RecoveryData): Promise<{ success: boolean; message: string; newPassword?: string }> => {
  if (typeof window !== 'undefined') {
    throw new Error('Account recovery can only be performed on the server side');
  }

  try {
    const user = await UserModel.findByUsername(recoveryData.username);
    if (!user) {
      return { success: false, message: 'User not found' };
    }

    if (recoveryData.recoveryMethod === 'privateKey') {
      if (!recoveryData.privateKey || !user.recoveryOptions.privateKey) {
        return { success: false, message: 'Invalid recovery method' };
      }

      const decryptedKey = decrypt(user.recoveryOptions.privateKey);
      if (decryptedKey !== recoveryData.privateKey) {
        return { success: false, message: 'Invalid private key' };
      }
    } else if (recoveryData.recoveryMethod === 'securityQuestions') {
      if (!recoveryData.securityAnswers || !user.recoveryOptions.securityQuestions) {
        return { success: false, message: 'Invalid recovery method' };
      }

      const questions = user.recoveryOptions.securityQuestions;
      if (questions.length !== recoveryData.securityAnswers.length) {
        return { success: false, message: 'Invalid number of security answers' };
      }

      for (let i = 0; i < questions.length; i++) {
        const answerHash = hashPassword(recoveryData.securityAnswers[i].toLowerCase().trim());
        if (questions[i].answerHash !== answerHash) {
          return { success: false, message: 'Incorrect security answers' };
        }
      }
    }

    // Generate new password
    const newPassword = generatePrivateKey().substring(0, 12);
    const newPasswordHash = hashPassword(newPassword);

    // Update password
    const updated = await UserModel.updatePassword(recoveryData.username, newPasswordHash);
    if (!updated) {
      return { success: false, message: 'Failed to update password' };
    }

    return { success: true, message: 'Password reset successful', newPassword };
  } catch (error) {
    console.error('Account recovery error:', error);
    return { success: false, message: 'Account recovery failed' };
  }
};
